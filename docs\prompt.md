## 🔧 BACKEND PROMPT
```markdown
## Backend: [FEATURE ADI] API

### İhtiyaç
[Ne için kullanılacak - örn: PIN ile kullanıcı girişi]

### Endpoints
- GET /api/[resource] → [N<PERSON> döner]
- POST /api/[resource] → [Ne alır, ne döner]
- PUT /api/[resource]/:id → [Ne günceller]
- DELETE /api/[resource]/:id → [Ne siler]

### Request/Response Örnekleri
```json
// POST /api/auth/login
Request: { "username": "admin", "pin": "1234" }
Response: { "token": "jwt...", "user": {...} }
```

### Teknik Gereksinimler
- Express + TypeScript (ES Modules)
- Prisma ORM (schema.prisma mevcut)
- Zod validation (her endpoint için)
- JWT authentication (protected routes için)
- Error middleware (consistent error response)
- HTTP status codes (200, 201, 400, 401, 404, 500)

### Business Rules
- [Varsa iş kuralları]
- [Örn: PIN 6 haneli olmalı]

### Notlar
- Response format: { success: boolean, data?: any, error?: string }
- [Diğer özel durumlar]
```

## 🎨 FRONTEND PROMPT
```markdown
## Frontend: [FEATURE ADI] Sayfası

### Tasarım
[Screenshot varsa ekle]

### UI Bileşenleri
- [Component 1: Ne yapar]
- [Component 2: Ne yapar]

### API Entegrasyonu
```typescript
// Kullanılacak endpoint'ler
GET /api/products → Product[]
POST /api/products → Product
```

### State Yönetimi
- Local State: [Form data, UI state]
- Global State (Zustand): [User, cart, vs]

### User Flow
1. Kullanıcı [X yapar]
2. Frontend [Y endpoint'ine istek atar]
3. Response gelince [Z olur]

### Teknik Gereksinimler
- React + TypeScript
- Radix UI components + Tailwind CSS
- Zustand (global state)
- Axios (interceptors ile)
- React Query veya useEffect (data fetching)
- Loading: Custom spinner with Tailwind
- Errors: Radix UI Toast/Alert Dialog
- Forms: Radix UI Form components + Tailwind styling

### Notlar
- Türkçe UI metinleri
- Responsive tasarım
- Error boundary ekle
- [Diğer özel durumlar]
```

