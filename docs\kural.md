ÖNCE BU KURALLARI ÖZÜMSE HAFIZANI TAZELE KOMPLE 

## KLASÖR YAPISI

Backend:
/routes     → auth.routes.ts
/services   → auth.service.ts  
/controllers → auth.controller.ts
/middlewares → auth.middleware.ts

Frontend:
/pages      → Dashboard/index.tsx
/components → common/Button.tsx, features/Cart.tsx
/hooks      → useAuth.ts
/services   → api.service.ts
/store      → authStore.ts

## İSİMLENDİRME

- Component: PascalCase → ProductCard.tsx
- Service/Hook: camelCase → useAuth.ts, authService.ts
- Types: PascalCase → Product.types.ts

## KURALLAR

1. Business logic → services'de
2. UI logic → components'de  
3. API calls → services'de
4. State → Zustand store'da
5. Her dosya tek iş yapsın

## YASAK

- 500+ satır dosya ❌
- Any type kullanma ❌
- Components'de API call ❌
- Inline styles ❌
## SOLID PRENSİPLERİ

1. **S** - Single Responsibility: Her class/fonksiyon TEK İŞ yapsın
2. **O** - Open/Closed: Yeni özellik eklerken eski kodu değiştirme
3. **L** - Liskov Substitution: Alt class'lar üst class'ın yerine geçebilmeli
4. **I** - Interface Segregation: Gereksiz method zorlama, interface'leri böl
5. **D** - Dependency Inversion: Concrete class'a değil, interface'e bağlan


🎯 API TEST KURALLARI - ÖZET
1. SERVER ÖNCE
npm run dev  # Background'da çalıştır
2. POWERSHELL SYNTAX
# JSON gönderirken MUTLAKA
-ContentType "application/json"

# Body hazırlama
$body = @{ key = "value" } | ConvertTo-Json
3. TEST SIRASI
Health check
Public endpoints
Auth (login)
Protected endpoints
4. ERROR HANDLING
try { 
    Invoke-RestMethod ... 
} catch { 
    $_.Exception.Response.StatusCode 
}
5. COMMON HATALAR
Server çalışmıyor ❌
Content-Type eksik ❌
Hardcode ID kullanma ❌
JSON syntax hatası ❌
6. GOLDEN RULE
Server çalıştır → Step by step → Content-Type unutma → Real data kullan

Frontendde Radix UI + Tailwind CSS v3 kullan.