Basit bir restoran POS projesi başlatıyorum.

Stack:
- Frontend: electron-vite + React 18 + TypeScript + Radix UI + Tailwind CSS v3
- Backend: Express + TypeScript + Prisma + PostgreSQL

İlk adım olarak:
1. Frontend ve backend klasörlerini ayır
2. Her ikisi için TypeScript config
3. Backend'de Express + Prisma setup
4. Frontend'de electron-vite (npm i electron-vite -D) + React setup
5. Basit bir health-check API ve frontend'de test
6. PostgreSQL veritabanı kurulumu
7. Prisma migration'ları yap
8. sana verilen prisma şemasını kullan  = 
9. ES Modules kullan (CommonJS yok) Electron ana sürecinde CommonJS yerine ES Modules kullan. - ES Modules (type: "module"), 
10. npm kullan
11. kurallara uy = 

TypeScript strict mode
Absolute imports (@/ prefix)
Environment based config

<PERSON><PERSON><PERSON><PERSON><PERSON> yapılar kull<PERSON>, minimal ve çalışan kod ver.