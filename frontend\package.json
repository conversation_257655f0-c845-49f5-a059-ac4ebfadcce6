{"name": "pos-frontend", "version": "1.0.0", "type": "module", "main": "dist-electron/main.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2"}, "devDependencies": {"electron": "^33.2.1", "electron-vite": "^2.3.0", "vite": "^6.1.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "^5.7.2", "@types/react": "^18.3.14", "@types/react-dom": "^18.3.1", "tailwindcss": "^3.4.1", "autoprefixer": "^10.4.20", "postcss": "^8.5.11"}}