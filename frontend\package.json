{"name": "pos-frontend", "version": "1.0.0", "type": "module", "main": "./out/main/index.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/react": "^18.3.14", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "electron": "^33.2.1", "electron-vite": "^2.3.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.1", "typescript": "^5.7.2", "vite": "^5.4.10"}}