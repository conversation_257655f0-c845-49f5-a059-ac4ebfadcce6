import React, { useState, useEffect } from 'react'

function App() {
  const [healthStatus, setHealthStatus] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Health check test
    const checkHealth = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/health')
        const data = await response.json()
        setHealthStatus(data)
      } catch (error) {
        setHealthStatus({ error: 'Backend bağlantısı başarısız' })
      } finally {
        setLoading(false)
      }
    }

    checkHealth()
  }, [])

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Restoran POS Sistemi
        </h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Backend Durum Kontrolü</h2>
          {loading ? (
            <p className="text-gray-600">Kontrol ediliyor...</p>
          ) : healthStatus?.error ? (
            <div className="text-red-600">
              <p>❌ {healthStatus.error}</p>
              <p className="text-sm mt-2">Backend sunucusunun çalıştığından emin olun.</p>
            </div>
          ) : (
            <div className="text-green-600">
              <p>✅ Backend bağlantısı başarılı</p>
              <p className="text-sm text-gray-600 mt-2">
                Durum: {healthStatus?.status} |
                Veritabanı: {healthStatus?.database || 'Bilinmiyor'} |
                Versiyon: {healthStatus?.version}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Son kontrol: {healthStatus?.timestamp}
              </p>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Proje Durumu</h2>
          <div className="space-y-2">
            <p>✅ Frontend (React + Electron) kuruldu</p>
            <p>✅ Backend (Express + TypeScript) kuruldu</p>
            <p>✅ Prisma şeması hazırlandı</p>
            <p>✅ PostgreSQL veritabanı bağlantısı kuruldu</p>
            <p>✅ Prisma migration tamamlandı</p>
            <p className="text-blue-600 font-medium mt-4">🚀 Sistem hazır! Geliştirmeye başlayabilirsiniz.</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
